#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 图片优化配置
const OPTIMIZATION_CONFIG = {
  // PNG优化设置
  png: {
    quality: '80-90',
    speed: 1,
    strip: true
  },
  // JPEG优化设置
  jpeg: {
    quality: 85,
    progressive: true,
    strip: true
  },
  // WebP转换设置
  webp: {
    quality: 85,
    method: 6,
    lossless: false
  }
}

// 需要优化的目录
const IMAGE_DIRECTORIES = [
  'static/images',
  'static'
]

// 支持的图片格式
const SUPPORTED_FORMATS = ['.png', '.jpg', '.jpeg']

class ImageOptimizer {
  constructor() {
    this.totalSaved = 0
    this.processedFiles = 0
    this.errors = []
  }

  /**
   * 检查必要的工具是否已安装
   */
  checkDependencies() {
    const tools = ['pngquant', 'jpegoptim', 'cwebp']
    const missing = []

    for (const tool of tools) {
      try {
        execSync(`which ${tool}`, { stdio: 'ignore' })
      } catch (error) {
        missing.push(tool)
      }
    }

    if (missing.length > 0) {
      console.log('❌ 缺少必要的图片优化工具:')
      console.log('请安装以下工具:')
      console.log('macOS: brew install pngquant jpegoptim webp')
      console.log('Ubuntu: sudo apt-get install pngquant jpegoptim webp')
      console.log('缺少工具:', missing.join(', '))
      return false
    }

    return true
  }

  /**
   * 获取文件大小
   */
  getFileSize(filePath) {
    try {
      return fs.statSync(filePath).size
    } catch (error) {
      return 0
    }
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 优化PNG图片
   */
  optimizePNG(filePath) {
    const originalSize = this.getFileSize(filePath)
    const backupPath = filePath + '.backup'
    
    try {
      // 创建备份
      fs.copyFileSync(filePath, backupPath)
      
      // 使用pngquant优化
      const command = `pngquant --quality=${OPTIMIZATION_CONFIG.png.quality} --speed=${OPTIMIZATION_CONFIG.png.speed} --force --output "${filePath}" "${filePath}"`
      execSync(command, { stdio: 'ignore' })
      
      const newSize = this.getFileSize(filePath)
      const saved = originalSize - newSize
      
      if (saved > 0) {
        this.totalSaved += saved
        console.log(`✅ PNG优化: ${path.basename(filePath)} - 节省 ${this.formatFileSize(saved)}`)
        // 删除备份
        fs.unlinkSync(backupPath)
        return true
      } else {
        // 恢复原文件
        fs.copyFileSync(backupPath, filePath)
        fs.unlinkSync(backupPath)
        return false
      }
    } catch (error) {
      // 恢复原文件
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, filePath)
        fs.unlinkSync(backupPath)
      }
      this.errors.push(`PNG优化失败: ${filePath} - ${error.message}`)
      return false
    }
  }

  /**
   * 优化JPEG图片
   */
  optimizeJPEG(filePath) {
    const originalSize = this.getFileSize(filePath)
    const backupPath = filePath + '.backup'
    
    try {
      // 创建备份
      fs.copyFileSync(filePath, backupPath)
      
      // 使用jpegoptim优化
      const command = `jpegoptim --max=${OPTIMIZATION_CONFIG.jpeg.quality} --strip-all "${filePath}"`
      execSync(command, { stdio: 'ignore' })
      
      const newSize = this.getFileSize(filePath)
      const saved = originalSize - newSize
      
      if (saved > 0) {
        this.totalSaved += saved
        console.log(`✅ JPEG优化: ${path.basename(filePath)} - 节省 ${this.formatFileSize(saved)}`)
        // 删除备份
        fs.unlinkSync(backupPath)
        return true
      } else {
        // 恢复原文件
        fs.copyFileSync(backupPath, filePath)
        fs.unlinkSync(backupPath)
        return false
      }
    } catch (error) {
      // 恢复原文件
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, filePath)
        fs.unlinkSync(backupPath)
      }
      this.errors.push(`JPEG优化失败: ${filePath} - ${error.message}`)
      return false
    }
  }

  /**
   * 转换为WebP格式
   */
  convertToWebP(filePath) {
    const webpPath = filePath.replace(/\.(png|jpg|jpeg)$/i, '.webp')
    
    // 如果WebP文件已存在，跳过
    if (fs.existsSync(webpPath)) {
      return false
    }
    
    try {
      const command = `cwebp -q ${OPTIMIZATION_CONFIG.webp.quality} -m ${OPTIMIZATION_CONFIG.webp.method} "${filePath}" -o "${webpPath}"`
      execSync(command, { stdio: 'ignore' })
      
      const originalSize = this.getFileSize(filePath)
      const webpSize = this.getFileSize(webpPath)
      
      if (webpSize > 0 && webpSize < originalSize) {
        const saved = originalSize - webpSize
        console.log(`✅ WebP转换: ${path.basename(filePath)} -> ${path.basename(webpPath)} - 节省 ${this.formatFileSize(saved)}`)
        return true
      } else {
        // 删除无效的WebP文件
        if (fs.existsSync(webpPath)) {
          fs.unlinkSync(webpPath)
        }
        return false
      }
    } catch (error) {
      this.errors.push(`WebP转换失败: ${filePath} - ${error.message}`)
      return false
    }
  }

  /**
   * 递归处理目录中的图片
   */
  processDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`⚠️  目录不存在: ${dirPath}`)
      return
    }

    const items = fs.readdirSync(dirPath)
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stat = fs.statSync(itemPath)
      
      if (stat.isDirectory()) {
        // 递归处理子目录
        this.processDirectory(itemPath)
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase()
        
        if (SUPPORTED_FORMATS.includes(ext)) {
          this.processedFiles++
          console.log(`🔄 处理: ${itemPath}`)
          
          // 优化原图片
          if (ext === '.png') {
            this.optimizePNG(itemPath)
          } else if (ext === '.jpg' || ext === '.jpeg') {
            this.optimizeJPEG(itemPath)
          }
          
          // 转换为WebP
          this.convertToWebP(itemPath)
        }
      }
    }
  }

  /**
   * 开始优化过程
   */
  async optimize() {
    console.log('🚀 开始图片优化...')
    
    // 检查依赖
    if (!this.checkDependencies()) {
      return false
    }
    
    // 处理每个目录
    for (const dir of IMAGE_DIRECTORIES) {
      console.log(`\n📁 处理目录: ${dir}`)
      this.processDirectory(dir)
    }
    
    // 输出结果
    console.log('\n📊 优化完成!')
    console.log(`处理文件数: ${this.processedFiles}`)
    console.log(`总节省空间: ${this.formatFileSize(this.totalSaved)}`)
    
    if (this.errors.length > 0) {
      console.log('\n❌ 错误列表:')
      this.errors.forEach(error => console.log(`  ${error}`))
    }
    
    return true
  }
}

// 运行优化
if (require.main === module) {
  const optimizer = new ImageOptimizer()
  optimizer.optimize().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = ImageOptimizer
