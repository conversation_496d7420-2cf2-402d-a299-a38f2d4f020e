<template>
  <div class="lazy-image-container" :class="{ 'loaded': imageLoaded }">
    <img
      v-if="shouldLoad"
      :src="optimizedSrc"
      :alt="alt"
      :class="imageClass"
      @load="onImageLoad"
      @error="onImageError"
      :loading="loading"
    />
    <div v-if="!imageLoaded && showPlaceholder" class="image-placeholder">
      <div class="placeholder-content">
        <fa :icon="['fas', 'image']" class="placeholder-icon" />
        <span class="placeholder-text">{{ placeholderText || 'Loading...' }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LazyImage',
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    imageClass: {
      type: String,
      default: ''
    },
    loading: {
      type: String,
      default: 'lazy',
      validator: value => ['lazy', 'eager'].includes(value)
    },
    showPlaceholder: {
      type: Boolean,
      default: true
    },
    placeholderText: {
      type: String,
      default: ''
    },
    webpSupport: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      imageLoaded: false,
      imageError: false,
      shouldLoad: false,
      observer: null,
      supportsWebP: false
    }
  },
  computed: {
    optimizedSrc() {
      if (!this.src) return ''
      
      // 如果支持WebP且启用WebP支持，尝试使用WebP版本
      if (this.supportsWebP && this.webpSupport && this.src.match(/\.(png|jpg|jpeg)$/i)) {
        const webpSrc = this.src.replace(/\.(png|jpg|jpeg)$/i, '.webp')
        return webpSrc
      }
      
      return this.src
    }
  },
  async mounted() {
    // 检测WebP支持
    this.supportsWebP = await this.checkWebPSupport()
    
    // 如果loading="eager"，立即加载
    if (this.loading === 'eager') {
      this.shouldLoad = true
      return
    }
    
    // 设置Intersection Observer进行懒加载
    this.setupIntersectionObserver()
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    setupIntersectionObserver() {
      if (!window.IntersectionObserver) {
        // 不支持Intersection Observer的浏览器直接加载
        this.shouldLoad = true
        return
      }
      
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.shouldLoad = true
              this.observer.disconnect()
            }
          })
        },
        {
          rootMargin: '50px 0px', // 提前50px开始加载
          threshold: 0.1
        }
      )
      
      this.observer.observe(this.$el)
    },
    
    checkWebPSupport() {
      // 缓存WebP支持检测结果
      if (typeof window.webpSupported !== 'undefined') {
        return Promise.resolve(window.webpSupported)
      }

      return new Promise((resolve) => {
        const webP = new Image()
        webP.onload = webP.onerror = () => {
          const supported = webP.height === 2
          window.webpSupported = supported // 缓存结果
          resolve(supported)
        }
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
      })
    },
    
    onImageLoad() {
      this.imageLoaded = true
      this.imageError = false
      this.$emit('load')
    },
    
    onImageError() {
      this.imageError = true
      this.imageLoaded = false
      this.$emit('error')
      
      // 如果WebP加载失败，尝试原始格式
      if (this.optimizedSrc !== this.src) {
        this.$nextTick(() => {
          const img = this.$el.querySelector('img')
          if (img) {
            img.src = this.src
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.lazy-image-container img {
  transition: opacity 0.3s ease;
  opacity: 0;
}

.lazy-image-container.loaded img {
  opacity: 1;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  min-height: 100px;
}

.placeholder-content {
  text-align: center;
  color: #6c757d;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.placeholder-text {
  font-size: 0.875rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .image-placeholder {
    min-height: 80px;
  }
  
  .placeholder-icon {
    font-size: 1.5rem;
  }
  
  .placeholder-text {
    font-size: 0.75rem;
  }
}
</style>
