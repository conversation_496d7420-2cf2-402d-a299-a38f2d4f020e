<template>
  <aside class="col-lg-5 col-xl-6 preview-panel" role="complementary">
    <div class="preview-container">
      <div class="card-preview-wrapper">
        <div
          id="yugiohcard-wrap"
          ref="yugiohcard-wrap"
          class="card-display"
          @mousemove="handleMouseMove"
          @mouseleave="handleMouseLeave"
          role="img"
          :aria-label="cardTitle || 'Yu-Gi-Oh! Card Preview'"
        >
          <canvas
            id="yugiohcard"
            ref="yugiohcard"
            class="card-canvas"
            :aria-label="`${cardTitle || 'Untitled Card'} - ${cardType} Card`"
          ></canvas>
        </div>
        
        <!-- Card Actions -->
        <div class="card-actions mt-3">
          <div class="d-flex justify-content-center gap-2">
            <button
              type="button"
              class="btn btn-outline-primary btn-sm"
              @click="toggleFullscreen"
              :aria-label="'Toggle Fullscreen'"
            >
              <fa :icon="['fas', 'expand']" class="me-1" />
              Fullscreen
            </button>
            
            <button
              type="button"
              class="btn btn-outline-secondary btn-sm"
              @click="copyToClipboard"
              :aria-label="'Copy to Clipboard'"
            >
              <fa :icon="['fas', 'copy']" class="me-1" />
              Copy
            </button>
            
            <button
              type="button"
              class="btn btn-outline-info btn-sm"
              @click="resetRotation"
              :aria-label="'Reset Rotation'"
            >
              <fa :icon="['fas', 'undo']" class="me-1" />
              Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script>
export default {
  name: 'CardPreview',
  props: {
    cardTitle: {
      type: String,
      default: ''
    },
    cardType: {
      type: String,
      default: 'Monster'
    }
  },
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', this.onFullscreenChange)
  },
  beforeDestroy() {
    document.removeEventListener('fullscreenchange', this.onFullscreenChange)
  },
  methods: {
    /**
     * 处理鼠标移动事件 - 3D效果
     */
    handleMouseMove(event) {
      const card = this.$refs['yugiohcard-wrap']
      if (!card) return

      const rect = card.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      const centerX = rect.width / 2
      const centerY = rect.height / 2
      
      const rotateX = (y - centerY) / 10
      const rotateY = (centerX - x) / 10
      
      // 应用3D变换
      card.style.transform = `
        perspective(1000px) 
        rotateX(${rotateX}deg) 
        rotateY(${rotateY}deg) 
        scale3d(1.02, 1.02, 1.02)
      `
      
      // 添加光泽效果
      card.style.filter = 'brightness(1.1)'
    },

    /**
     * 处理鼠标离开事件
     */
    handleMouseLeave() {
      const card = this.$refs['yugiohcard-wrap']
      if (!card) return

      // 重置变换
      card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)'
      card.style.filter = 'brightness(1)'
    },

    /**
     * 重置卡片旋转
     */
    resetRotation() {
      const card = this.$refs['yugiohcard-wrap']
      if (!card) return

      card.style.transform = ''
      card.style.filter = ''
    },

    /**
     * 切换全屏模式
     */
    async toggleFullscreen() {
      const canvas = this.$refs.yugiohcard
      if (!canvas) return

      try {
        if (!document.fullscreenElement) {
          await canvas.requestFullscreen()
        } else {
          await document.exitFullscreen()
        }
      } catch (error) {
        console.warn('Fullscreen operation failed:', error)
      }
    },

    /**
     * 复制卡片图片到剪贴板
     */
    async copyToClipboard() {
      const canvas = this.$refs.yugiohcard
      if (!canvas) return

      try {
        // 转换为Blob
        const blob = await new Promise(resolve => {
          canvas.toBlob(resolve, 'image/png')
        })

        if (blob && navigator.clipboard && window.ClipboardItem) {
          await navigator.clipboard.write([
            new ClipboardItem({ 'image/png': blob })
          ])
          
          // 显示成功提示
          this.$emit('copy-success')
        } else {
          // 降级方案：触发下载
          this.$emit('copy-fallback')
        }
      } catch (error) {
        console.warn('Copy to clipboard failed:', error)
        this.$emit('copy-error', error)
      }
    },

    /**
     * 全屏状态变化处理
     */
    onFullscreenChange() {
      this.isFullscreen = !!document.fullscreenElement
    },

    /**
     * 获取Canvas引用（供父组件使用）
     */
    getCanvas() {
      return this.$refs.yugiohcard
    },

    /**
     * 获取卡片容器引用（供父组件使用）
     */
    getCardContainer() {
      return this.$refs['yugiohcard-wrap']
    }
  }
}
</script>

<style scoped>
.preview-panel {
  padding: 1rem;
}

.preview-container {
  position: sticky;
  top: 100px;
  height: fit-content;
}

.card-preview-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.card-preview-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

.card-display {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  cursor: pointer;
}

.card-canvas {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

.card-canvas:hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
}

.card-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.card-actions .btn {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.card-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 全屏模式样式 */
.card-canvas:fullscreen {
  width: 100vw;
  height: 100vh;
  object-fit: contain;
  background: #000;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .preview-container {
    position: static;
    margin-bottom: 2rem;
  }
  
  .card-preview-wrapper {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .card-preview-wrapper {
    padding: 1rem;
    border-radius: 15px;
  }
  
  .card-actions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* 动画效果 */
@keyframes cardGlow {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }
}

.card-canvas.generating {
  animation: cardGlow 2s ease-in-out infinite;
}

/* 加载状态 */
.card-display.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
