<template>
  <section class="col-lg-7 col-xl-6 editor-panel" role="form">
    <div class="editor-container">
      <div class="editor-content">
        <!-- Card Settings Form -->
        <form @submit.prevent="doDrawCard" class="card-settings-form">
          <!-- Basic Settings Row -->
          <b-row class="my-3">
            <b-col cols="12" sm="6" lg="4" class="px-2">
              <div class="form-check px-0">
                <label>{{ ui[uiLang].square_foil_stamp }}</label>
                <b-form-checkbox
                  v-model="holo"
                  :class="{'checkbox-wrap': true, 'active': holo}"
                  button
                >{{ holo ? ui[uiLang].on : ui[uiLang].off }}</b-form-checkbox>
              </div>
            </b-col>
            <b-col cols="12" sm="6" lg="4" class="px-2">
              <label>{{ ui[uiLang].rarity }}</label>
              <b-form-select v-model="cardRare" :options="cardRareOpts"></b-form-select>
            </b-col>
            <b-col cols="12" sm="6" lg="4" class="px-2">
              <label>{{ ui[uiLang].title_color }}</label>
              <b-form-input v-model="titleColor" type="color"></b-form-input>
            </b-col>
          </b-row>

          <!-- Card Name -->
          <b-row class="my-3">
            <b-col class="px-2">
              <label>{{ ui[uiLang].card_name }}</label>
              <b-form-input v-model="cardTitle"></b-form-input>
            </b-col>
          </b-row>

          <!-- Card Image Upload -->
          <b-row class="my-3">
            <b-col class="px-2">
              <b-form-file
                v-model="cardImg"
                :state="Boolean(cardImg)"
                :placeholder="ui[uiLang].upload_image"
                browse="✚"
                accept="image/*"
                :drop-placeholder="ui[uiLang].drag_and_drop"
              ></b-form-file>
            </b-col>
          </b-row>

          <!-- Card Type and Subtype -->
          <b-row class="my-3">
            <b-col cols="12" sm="6" lg="3" class="px-2">
              <label>{{ ui[uiLang].card_type }}</label>
              <b-form-select v-model="cardType" :options="cardTypeOpts"></b-form-select>
            </b-col>
            <b-col cols="12" sm="6" lg="3" class="px-2">
              <label>{{ ui[uiLang].card_subtype }}</label>
              <b-form-select v-model="cardSubtype" :options="cardSubtypeOpts[cardType]"></b-form-select>
            </b-col>
            <b-col v-show="cardType==='Monster'" cols="12" sm="6" lg="3" class="px-2">
              <label>{{ ui[uiLang].card_effect }}</label>
              <b-form-select v-model="cardEff1" :options="cardEff1Opts"></b-form-select>
            </b-col>
            <b-col v-show="cardType==='Monster'" cols="12" sm="6" lg="3" class="px-2">
              <label>&emsp;</label>
              <b-form-select v-model="cardEff2" :options="cardEff2Opts"></b-form-select>
            </b-col>
          </b-row>

          <!-- Monster Attributes -->
          <b-row v-show="cardType==='Monster'" class="my-3">
            <b-col cols="12" lg="6" class="px-2">
              <label>{{ ui[uiLang].card_attribute }}</label>
              <b-form-select v-model="cardAttr" :options="cardAttrOpts"></b-form-select>
            </b-col>
            <b-col cols="6" lg="3" class="px-2">
              <div class="form-check px-0">
                <label>{{ ui[uiLang].card_race_type }}</label>
                <b-form-checkbox 
                  v-model="cardCustomRaceEnabled" 
                  :class="{'checkbox-wrap': true, 'active': cardCustomRaceEnabled}" 
                  button
                >{{ ui[uiLang].custom }}</b-form-checkbox>
              </div>
            </b-col>
            <b-col v-show="!cardCustomRaceEnabled" cols="6" lg="3" class="px-2">
              <label>&emsp;</label>
              <b-form-select v-model="cardRace" :options="cardRaceOpts"></b-form-select>
            </b-col>
            <b-col v-show="cardCustomRaceEnabled" cols="6" lg="3" class="px-2">
              <label>&emsp;</label>
              <b-form-input 
                v-model="cardCustomRace"
                type="text"
                maxlength="8"
                :placeholder="ui[uiLang].plz_input_race_type"
              />
            </b-col>
          </b-row>

          <!-- Pendulum and Level Settings -->
          <b-row class="my-3">
            <b-col v-show="canPendulumEnabled" cols="6" lg="4" class="px-2">
              <div class="form-check px-0">
                <label>&emsp;</label>
                <b-form-checkbox 
                  v-model="Pendulum" 
                  :class="{'checkbox-wrap': true, 'active': Pendulum}" 
                  button
                >{{ ui[uiLang].pendulum }}</b-form-checkbox>
              </div>
            </b-col>
            <b-col v-show="cardType==='Monster'" cols="6" lg="4" class="px-2">
              <div class="form-check px-0">
                <label>&emsp;</label>
                <b-form-checkbox 
                  v-model="Special" 
                  :class="{'checkbox-wrap': true, 'active': Special}" 
                  button
                >{{ ui[uiLang].special_summon }}</b-form-checkbox>
              </div>
            </b-col>
            <b-col v-show="cardType==='Monster' && !isLinkMonster" cols="12" lg="4" class="px-2">
              <label>{{ ui[uiLang].lavel_and_rank }}</label>
              <b-form-select v-model="cardLevel" :options="cardLevelOpts"></b-form-select>
            </b-col>
          </b-row>

          <!-- ATK/DEF Settings -->
          <b-row class="my-3">     
            <b-col v-show="cardType==='Monster'" cols="4" class="px-2">
              <label>{{ ui[uiLang].attack }}</label>
              <b-form-input v-model="cardATK" type="text" maxlength="6"></b-form-input>
            </b-col>
            <b-col v-show="cardType==='Monster' && !isLinkMonster" cols="4" class="px-2">
              <label>{{ ui[uiLang].defence }}</label>
              <b-form-input v-model="cardDEF" type="text" maxlength="6"></b-form-input>
            </b-col>
            <b-col cols="4" class="px-2">
              <label>{{ ui[uiLang].text_size }}</label>
              <b-form-input v-model="infoSize" type="number"></b-form-input>
            </b-col>
          </b-row>

          <!-- Card Description -->
          <b-row class="my-3">
            <b-col class="px-2">
              <b-form-textarea v-model="cardInfo" rows="5"></b-form-textarea>
            </b-col>
          </b-row>
          
          <!-- Action Buttons -->
          <b-row class="my-3">
            <b-col class="px-2">
              <div class="button-group d-flex flex-wrap gap-2 align-items-center justify-content-center justify-content-sm-start">
                <button
                  type="button"
                  class="btn btn-info"
                  @click="doDrawCard"
                  :aria-label="ui[uiLang].generate"
                >
                  <fa :icon="['fas', 'magic']" class="me-1" />
                  {{ ui[uiLang].generate }}
                </button>

                <button
                  type="button"
                  class="btn btn-success"
                  @click="downloadImage"
                  :aria-label="ui[uiLang].download"
                >
                  <fa :icon="['fas', 'download']" class="me-1" />
                  {{ ui[uiLang].download }}
                </button>

                <button
                  type="button"
                  class="btn btn-outline-danger"
                  @click="resetToDefault"
                  :aria-label="ui[uiLang].reset_to_default"
                >
                  <fa :icon="['fas', 'undo']" class="me-1" />
                  {{ ui[uiLang].reset_to_default }}
                </button>
              </div>

              <small class="text-muted d-block mt-2 text-center text-sm-start">
                <fa :icon="['fas', 'info-circle']" class="me-1" />
                {{ ui[uiLang].auto_gen_note }}
              </small>
            </b-col>
          </b-row>
        </form>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'CardEditor',
  props: {
    // 从父组件接收的数据
    ui: {
      type: Object,
      required: true
    },
    uiLang: {
      type: String,
      required: true
    },
    // 卡片数据
    holo: Boolean,
    cardRare: String,
    titleColor: String,
    cardTitle: String,
    cardImg: File,
    cardType: String,
    cardSubtype: String,
    cardEff1: String,
    cardEff2: String,
    cardAttr: String,
    cardCustomRaceEnabled: Boolean,
    cardCustomRace: String,
    cardRace: String,
    Pendulum: Boolean,
    Special: Boolean,
    cardLevel: String,
    cardATK: String,
    cardDEF: String,
    infoSize: String,
    cardInfo: String,
    // 选项数据
    cardRareOpts: Object,
    cardTypeOpts: Object,
    cardSubtypeOpts: Object,
    cardEff1Opts: Object,
    cardEff2Opts: Object,
    cardAttrOpts: Array,
    cardRaceOpts: Object,
    cardLevelOpts: Array
  },
  computed: {
    canPendulumEnabled() {
      return this.cardType === 'Monster' && !["Slifer", "Ra", "Obelisk", "LDragon"].includes(this.cardSubtype)
    },
    isLinkMonster() {
      return this.cardType === 'Monster' && this.cardSubtype === 'Link'
    }
  },
  methods: {
    doDrawCard() {
      this.$emit('draw-card')
    },
    downloadImage() {
      this.$emit('download-image')
    },
    resetToDefault() {
      this.$emit('reset-to-default')
    }
  }
}
</script>

<style scoped>
.editor-panel {
  padding: 1rem;
}

.editor-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-settings-form {
  max-width: 100%;
}

.button-group {
  gap: 0.5rem;
}

.button-group .btn {
  min-width: 120px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .editor-container {
    padding: 1rem;
  }
  
  .button-group .btn {
    min-width: 100px;
    font-size: 0.875rem;
  }
}
</style>
