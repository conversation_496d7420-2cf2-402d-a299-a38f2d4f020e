/* 性能优化相关样式 */

/* GPU 加速 */
.card-display,
.card-canvas,
.lazy-image-container {
  transform: translateZ(0);
  will-change: transform;
}

/* 减少重绘 */
.form-control,
.btn,
.card {
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 优化滚动性能 */
.main-content {
  contain: layout style paint;
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 减少移动端的阴影效果以提升性能 */
  .card-canvas {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  }
  
  .card-display:hover .card-canvas {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
  }
  
  /* 优化移动端表单 */
  .form-control {
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  /* 减少移动端动画 */
  .card-display {
    transition: transform 0.2s ease;
  }
  
  .card-display:hover {
    transform: perspective(1000px) rotateX(2deg) rotateY(5deg);
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .card-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 减少动画对性能的影响 */
@media (prefers-reduced-motion: reduce) {
  .card-display,
  .lazy-image-container,
  .btn,
  .form-control {
    transition: none !important;
    animation: none !important;
  }
}

/* 优化字体渲染 */
.card-settings-form,
.card-preview-wrapper {
  text-rendering: optimizeSpeed;
  font-smooth: never;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 内容可见性优化 */
.features-section,
.how-to-use-section,
.faq-section {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 图片加载优化 */
img {
  content-visibility: auto;
  contain-intrinsic-size: 0 200px;
}

/* 表单性能优化 */
.form-control:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Canvas 优化 */
canvas {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

/* 布局稳定性 */
.card-editor-container {
  min-height: 100vh;
  contain: layout;
}

.card-preview-wrapper {
  min-height: 600px;
  contain: layout style;
}

/* 防止布局偏移 */
.navbar-brand img {
  width: 40px;
  height: 40px;
}

.feature-icon {
  width: 60px;
  height: 60px;
}

/* 优化加载状态 */
.loading-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 深色模式性能优化 */
@media (prefers-color-scheme: dark) {
  .loading-placeholder {
    background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
    background-size: 200% 100%;
  }
}

/* 打印优化 */
@media print {
  .card-display,
  .card-canvas {
    box-shadow: none !important;
    transform: none !important;
  }
  
  .navbar,
  .features-section,
  .how-to-use-section,
  .faq-section,
  footer {
    display: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .card-canvas {
    /* 在高对比度模式下也保持无边框，使用阴影来提供对比 */
    border: none !important;
    box-shadow: 0 0 0 2px currentColor, 0 15px 35px rgba(0, 0, 0, 0.6);
  }

  .btn {
    border-width: 2px;
  }
}

/* 减少重排和重绘 */
.form-row {
  contain: layout;
}

.btn-group {
  contain: layout;
}

/* 优化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
  }
  
  .form-control {
    min-height: 44px;
  }
  
  /* 移除悬停效果 */
  .card-display:hover {
    transform: none;
  }
  
  .btn:hover {
    transform: none;
  }
}

/* 内存优化 */
.large-content {
  contain: strict;
}

/* 网络优化提示 */
@media (prefers-reduced-data: reduce) {
  .card-canvas {
    image-rendering: pixelated;
  }
  
  /* 减少不必要的视觉效果 */
  .card-display {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 焦点管理优化 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 键盘导航优化 */
.btn:focus-visible,
.form-control:focus-visible {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

/* 性能监控辅助 */
.performance-marker {
  contain: strict;
  content-visibility: auto;
}
