/* 生产环境修复样式 - 确保关键样式在部署后正确应用 */

/* 1. Canvas样式修复 - 最高优先级 */
canvas#yugiohcard,
canvas.card-canvas,
#yugiohcard.card-canvas,
.card-canvas {
  /* 强制透明背景，移除任何边框 */
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
  
  /* 确保正确的尺寸和比例 */
  max-width: 100% !important;
  height: auto !important;
  aspect-ratio: 1000 / 1450 !important;
  
  /* 保持专业的阴影效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4) !important;
  
  /* 平滑过渡效果 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 2. 悬停效果修复 */
.card-display:hover canvas#yugiohcard,
.card-display:hover canvas.card-canvas,
.card-display:hover #yugiohcard.card-canvas,
.card-display:hover .card-canvas {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8), 0 10px 25px rgba(0, 0, 0, 0.6) !important;
  background: transparent !important;
  border: none !important;
}

/* 3. 响应式修复 */
@media (max-width: 1200px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 350px !important;
  }
}

@media (max-width: 992px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 300px !important;
  }
}

@media (max-width: 768px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 280px !important;
    /* 移动端减少阴影以提升性能 */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  }
  
  .card-display:hover canvas#yugiohcard,
  .card-display:hover canvas.card-canvas,
  .card-display:hover #yugiohcard.card-canvas,
  .card-display:hover .card-canvas {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4) !important;
  }
}

/* 4. 高对比度模式修复 */
@media (prefers-contrast: high) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    border: none !important;
    background: transparent !important;
    /* 使用阴影提供对比度而不是边框 */
    box-shadow: 0 0 0 2px currentColor, 0 15px 35px rgba(0, 0, 0, 0.6) !important;
  }
}

/* 5. 打印模式修复 */
@media print {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    transform: none !important;
  }
}

/* 6. 减少动画偏好修复 */
@media (prefers-reduced-motion: reduce) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    transition: none !important;
    animation: none !important;
  }
}

/* 7. 触摸设备修复 */
@media (hover: none) and (pointer: coarse) {
  .card-display:hover canvas#yugiohcard,
  .card-display:hover canvas.card-canvas,
  .card-display:hover #yugiohcard.card-canvas,
  .card-display:hover .card-canvas {
    transform: none !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4) !important;
  }
}

/* 8. 网络数据节省模式修复 */
@media (prefers-reduced-data: reduce) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* 9. 深色模式兼容性 */
@media (prefers-color-scheme: dark) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    background: transparent !important;
    border: none !important;
  }
}

/* 10. 强制覆盖任何可能的冲突样式 */
.card-preview-wrapper canvas#yugiohcard,
.card-preview-wrapper canvas.card-canvas,
.card-preview-wrapper #yugiohcard.card-canvas,
.card-preview-wrapper .card-canvas,
.card-display canvas#yugiohcard,
.card-display canvas.card-canvas,
.card-display #yugiohcard.card-canvas,
.card-display .card-canvas {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
}

/* 11. 确保Canvas元素的渲染质量 */
canvas#yugiohcard,
canvas.card-canvas {
  image-rendering: -webkit-optimize-contrast !important;
  image-rendering: crisp-edges !important;
  image-rendering: pixelated !important;
}

/* 12. 防止任何Bootstrap或其他框架的样式干扰 */
.container canvas#yugiohcard,
.container canvas.card-canvas,
.container #yugiohcard.card-canvas,
.container .card-canvas,
.row canvas#yugiohcard,
.row canvas.card-canvas,
.row #yugiohcard.card-canvas,
.row .card-canvas,
.col canvas#yugiohcard,
.col canvas.card-canvas,
.col #yugiohcard.card-canvas,
.col .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 13. 确保在所有可能的父容器中都正确显示 */
* canvas#yugiohcard,
* canvas.card-canvas,
* #yugiohcard.card-canvas,
* .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 14. 最终保险措施 - 使用最高优先级选择器 */
html body .container .row .col .card-preview-wrapper .card-display canvas#yugiohcard,
html body .container .row .col .card-preview-wrapper .card-display canvas.card-canvas,
html body .container .row .col .card-preview-wrapper .card-display #yugiohcard.card-canvas,
html body .container .row .col .card-preview-wrapper .card-display .card-canvas {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
}
