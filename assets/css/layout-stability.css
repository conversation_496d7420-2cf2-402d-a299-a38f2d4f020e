/* 布局稳定性优化 CSS */

/* 1. 防止字体加载导致的布局偏移 */
body {
  font-family: 'Noto Sans SC', 'Noto Sans TC', 'Noto Sans JP', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-display: swap;
}

/* 2. 为图片预留空间，防止加载时的布局偏移 */
.brand-logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
  /* 预留空间防止布局偏移 */
  min-width: 60px;
  min-height: 60px;
}

/* 3. 卡片预览区域固定尺寸 */
.card-canvas {
  /* 固定宽高比，防止加载时布局偏移 */
  aspect-ratio: 1000 / 1450;
  width: 100%;
  max-width: 400px;
  height: auto;
  /* 移除背景色和边框，保持透明 */
  background: transparent !important;
  border: none !important;
}

/* 4. 表单元素固定高度 */
.form-control {
  min-height: 38px;
}

.btn {
  min-height: 38px;
}

/* 5. 导航栏固定高度 */
.main-navbar {
  height: 80px;
  min-height: 80px;
}

.navbar-brand-custom {
  height: 60px;
  display: flex;
  align-items: center;
}

/* 6. 锚点偏移，避免固定导航栏遮挡内容 */
.anchor-section {
  scroll-margin-top: 100px;
  padding-top: 2rem;
}

/* 7. 容器最小高度，防止内容跳动 */
.editor-container {
  min-height: 600px;
}

.preview-container {
  min-height: 500px;
}

/* 8. 加载状态占位符 */
.loading-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 9. 图片懒加载占位符 */
.lazy-image-container {
  position: relative;
  overflow: hidden;
}

.lazy-image-container img {
  transition: opacity 0.3s ease;
}

.lazy-image-container:not(.loaded) img {
  opacity: 0;
}

.lazy-image-container.loaded img {
  opacity: 1;
}

/* 10. 响应式布局稳定性 */
@media (max-width: 1200px) {
  .card-canvas {
    max-width: 350px;
  }
}

@media (max-width: 992px) {
  .card-canvas {
    max-width: 300px;
  }
  
  .editor-container {
    min-height: 500px;
  }
  
  .preview-container {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .brand-logo {
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
  }
  
  .main-navbar {
    height: 70px;
    min-height: 70px;
  }
  
  .anchor-section {
    scroll-margin-top: 80px;
  }
  
  .card-canvas {
    max-width: 280px;
  }
}

/* 11. 防止内容溢出导致的水平滚动 */
.container-fluid {
  overflow-x: hidden;
}

.row {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-auto, .col-sm, .col-sm-1, .col-sm-2, .col-sm-3,
.col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8,
.col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-auto,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4,
.col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9,
.col-md-10, .col-md-11, .col-md-12, .col-md-auto,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4,
.col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9,
.col-lg-10, .col-lg-11, .col-lg-12, .col-lg-auto,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4,
.col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9,
.col-xl-10, .col-xl-11, .col-xl-12, .col-xl-auto {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

/* 12. 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 13. 减少重绘和重排 */
* {
  box-sizing: border-box;
}

.card-display {
  will-change: transform;
  transform: translateZ(0);
}

/* 14. 优化动画性能 */
.card-display,
.card-canvas,
.btn {
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 15. 防止Flash of Unstyled Content (FOUC) */
.v-cloak {
  display: none !important;
}

/* 16. 确保关键内容优先渲染 */
.critical-content {
  contain: layout style paint;
}

/* 17. 优化表单布局稳定性 */
.form-group {
  margin-bottom: 1rem;
  min-height: 60px;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  min-height: 20px;
}

/* 18. 按钮组布局稳定性 */
.button-group {
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* 19. 导航链接稳定性 */
.nav-link-custom {
  min-height: 40px;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
}

/* 20. 页脚稳定性 */
.site-footer {
  min-height: 200px;
}

/* 21. 特殊情况处理 */
@supports (aspect-ratio: 1 / 1) {
  .card-canvas {
    aspect-ratio: 1000 / 1450;
  }
}

/* 22. 打印样式优化 */
@media print {
  .anchor-section {
    scroll-margin-top: 0;
    padding-top: 0;
  }
  
  .main-navbar {
    display: none;
  }
}

/* 23. 高对比度模式支持 */
@media (prefers-contrast: high) {
  .card-canvas {
    border: 2px solid;
  }
}

/* 24. 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}
