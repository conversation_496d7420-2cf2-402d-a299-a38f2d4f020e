/* Vercel部署专用修复样式 */

/* 1. 强制字体加载和显示 */
@font-face {
  font-family: 'YuGiOh-Matrix';
  src: url('/fonts/MatrixBoldSmallCaps.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-JP';
  src: url('/fonts/jp.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-JP2';
  src: url('/fonts/jp2.otf') format('opentype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-EN';
  src: url('/fonts/en.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-EN2';
  src: url('/fonts/en2.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-EN3';
  src: url('/fonts/en3.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-CN';
  src: url('/fonts/cn.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-ZH';
  src: url('/fonts/zh.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-Link';
  src: url('/fonts/link.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

/* 2. Vercel环境下的Canvas修复 - 最高优先级 */
html[data-nuxt-ssr] canvas#yugiohcard,
html[data-nuxt-ssr] canvas.card-canvas,
html[data-nuxt-ssr] #yugiohcard.card-canvas,
html[data-nuxt-ssr] .card-canvas,
[data-server-rendered="true"] canvas#yugiohcard,
[data-server-rendered="true"] canvas.card-canvas,
[data-server-rendered="true"] #yugiohcard.card-canvas,
[data-server-rendered="true"] .card-canvas,
.nuxt-loading canvas#yugiohcard,
.nuxt-loading canvas.card-canvas,
.nuxt-loading #yugiohcard.card-canvas,
.nuxt-loading .card-canvas,
canvas#yugiohcard,
canvas.card-canvas,
#yugiohcard.card-canvas,
.card-canvas {
  /* 强制透明背景 */
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  
  /* 强制移除边框 */
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
  
  /* 确保正确的尺寸 */
  max-width: 100% !important;
  height: auto !important;
  aspect-ratio: 1000 / 1450 !important;
  
  /* 保持阴影效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4) !important;
  
  /* 平滑过渡 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 3. Vercel环境下的字体强制应用 */
.nuxt-loading,
[data-server-rendered="true"],
html[data-nuxt-ssr] {
  font-family: 'Noto Sans TC', 'Noto Sans SC', 'Noto Sans JP', 'YuGiOh-ZH', 'YuGiOh-CN', 'YuGiOh-JP', sans-serif !important;
}

/* 4. 确保在Vercel上CSS加载完成后立即应用 */
.css-loaded canvas#yugiohcard,
.css-loaded canvas.card-canvas,
.css-loaded #yugiohcard.card-canvas,
.css-loaded .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 5. 防止Vercel的CSS优化影响关键样式 */
* canvas#yugiohcard,
* canvas.card-canvas,
* #yugiohcard.card-canvas,
* .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 6. Vercel特定的布局修复 */
.vercel-deployment canvas#yugiohcard,
.vercel-deployment canvas.card-canvas,
.vercel-deployment #yugiohcard.card-canvas,
.vercel-deployment .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 7. 确保在所有可能的Vercel渲染状态下都正确显示 */
.nuxt-progress canvas#yugiohcard,
.nuxt-progress canvas.card-canvas,
.nuxt-progress #yugiohcard.card-canvas,
.nuxt-progress .card-canvas,
.layout-default canvas#yugiohcard,
.layout-default canvas.card-canvas,
.layout-default #yugiohcard.card-canvas,
.layout-default .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 8. 强制覆盖任何可能的Bootstrap或框架样式 */
.container canvas#yugiohcard,
.container canvas.card-canvas,
.container #yugiohcard.card-canvas,
.container .card-canvas,
.row canvas#yugiohcard,
.row canvas.card-canvas,
.row #yugiohcard.card-canvas,
.row .card-canvas,
.col canvas#yugiohcard,
.col canvas.card-canvas,
.col #yugiohcard.card-canvas,
.col .card-canvas,
.col-lg-6 canvas#yugiohcard,
.col-lg-6 canvas.card-canvas,
.col-lg-6 #yugiohcard.card-canvas,
.col-lg-6 .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 9. Vercel环境下的响应式修复 */
@media (max-width: 1200px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 350px !important;
    background: transparent !important;
    border: none !important;
  }
}

@media (max-width: 992px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 300px !important;
    background: transparent !important;
    border: none !important;
  }
}

@media (max-width: 768px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 280px !important;
    background: transparent !important;
    border: none !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  }
}

/* 10. 确保字体在Vercel上正确加载的回退机制 */
body {
  font-family: 'Noto Sans TC', 'Noto Sans SC', 'Noto Sans JP', 'YuGiOh-ZH', 'YuGiOh-CN', 'YuGiOh-JP', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 11. 强制在Vercel环境下立即应用样式 */
html {
  --vercel-canvas-bg: transparent !important;
  --vercel-canvas-border: none !important;
}

canvas#yugiohcard,
canvas.card-canvas,
#yugiohcard.card-canvas,
.card-canvas {
  background: var(--vercel-canvas-bg) !important;
  border: var(--vercel-canvas-border) !important;
}

/* 12. 最终保险措施 - 使用内联样式级别的优先级 */
canvas#yugiohcard[style],
canvas.card-canvas[style],
#yugiohcard.card-canvas[style],
.card-canvas[style] {
  background: transparent !important;
  border: none !important;
}

/* 13. Vercel部署后的字体加载确认 */
.font-loaded canvas#yugiohcard,
.font-loaded canvas.card-canvas,
.font-loaded #yugiohcard.card-canvas,
.font-loaded .card-canvas {
  font-family: 'YuGiOh-Matrix', 'YuGiOh-ZH', 'YuGiOh-CN', 'YuGiOh-JP', 'Noto Sans TC', sans-serif !important;
}

/* 14. 确保在Vercel的CDN缓存更新后样式正确 */
.cache-updated canvas#yugiohcard,
.cache-updated canvas.card-canvas,
.cache-updated #yugiohcard.card-canvas,
.cache-updated .card-canvas {
  background: transparent !important;
  border: none !important;
}
