{"name": "yugioh_card_maker", "version": "1.0.0", "private": true, "scripts": {"dev": "NODE_OPTIONS='--openssl-legacy-provider' nuxt", "build": "NODE_OPTIONS='--openssl-legacy-provider' nuxt build", "start": "NODE_OPTIONS='--openssl-legacy-provider' nuxt start", "generate": "NODE_OPTIONS='--openssl-legacy-provider' nuxt generate", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint": "npm run lint:js", "build:gh-pages": "cross-env DEPLOY_ENV=GH_PAGES NODE_OPTIONS='--openssl-legacy-provider' nuxt build", "generate:gh-pages": "cross-env DEPLOY_ENV=GH_PAGES NODE_OPTIONS='--openssl-legacy-provider' nuxt generate", "vercel-build": "NODE_OPTIONS='--openssl-legacy-provider' nuxt generate", "sitemap": "node scripts/generate-sitemap.js", "sitemap:validate": "node scripts/validate-sitemap.js", "build:full": "npm run generate && npm run sitemap && cp static/sitemap.xml dist/sitemap.xml && npm run sitemap:validate"}, "dependencies": {"@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/free-regular-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/sitemap": "^2.4.0", "bootstrap": "^4.6.0", "bootstrap-vue": "^2.21.2", "core-js": "^3.15.1", "cross-env": "^7.0.3", "nuxt": "^2.15.7", "nuxt-font-loader": "^1.1.3", "nuxt-fontawesome": "^0.4.0"}, "devDependencies": {"@babel/eslint-parser": "^7.14.7", "@nuxtjs/eslint-config": "^6.0.1", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/vercel-builder": "^0.24.0", "eslint": "^7.29.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-vue": "^7.12.1", "image-webpack-loader": "^8.1.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-webp": "^7.0.0", "postcss": "^7.0.39", "postcss-import": "^12.0.1", "postcss-url": "^8.0.0", "prettier": "^2.3.2", "xmldom": "^0.6.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}