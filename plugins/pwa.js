/**
 * PWA Plugin for Yu-Gi-Oh Card Maker
 * Registers Service Worker and handles PWA functionality
 */

export default ({ app, router }) => {
  // Only run on client-side
  if (!process.client) return

  // Check if Service Worker is supported
  if (!('serviceWorker' in navigator)) {
    console.log('PWA: Service Worker not supported')
    return
  }

  // Register Service Worker
  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      console.log('PWA: Service Worker registered successfully', registration)

      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available
              showUpdateNotification()
            }
          })
        }
      })

      // Handle controller change
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // New Service Worker has taken control
        window.location.reload()
      })

      return registration
    } catch (error) {
      console.error('PWA: Service Worker registration failed', error)
    }
  }

  // Show update notification
  const showUpdateNotification = () => {
    // Create a simple notification
    const notification = document.createElement('div')
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: #007bff;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 300px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="margin-bottom: 0.5rem; font-weight: bold;">
          🎉 New version available!
        </div>
        <div style="margin-bottom: 1rem; font-size: 0.9rem;">
          A new version of Yu-Gi-Oh Card Maker is ready.
        </div>
        <div>
          <button id="update-btn" style="
            background: white;
            color: #007bff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 0.5rem;
            font-weight: bold;
          ">Update</button>
          <button id="dismiss-btn" style="
            background: transparent;
            color: white;
            border: 1px solid white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
          ">Later</button>
        </div>
      </div>
    `

    document.body.appendChild(notification)

    // Handle update button
    notification.querySelector('#update-btn').addEventListener('click', () => {
      // Tell the waiting Service Worker to skip waiting
      if (navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' })
      }
      notification.remove()
    })

    // Handle dismiss button
    notification.querySelector('#dismiss-btn').addEventListener('click', () => {
      notification.remove()
    })

    // Auto dismiss after 10 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove()
      }
    }, 10000)
  }

  // Install prompt handling
  let deferredPrompt = null

  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault()
    
    // Stash the event so it can be triggered later
    deferredPrompt = e
    
    // Show install button
    showInstallButton()
  })

  // Show install button
  const showInstallButton = () => {
    // Check if install button already exists
    if (document.querySelector('#pwa-install-btn')) return

    const installBtn = document.createElement('button')
    installBtn.id = 'pwa-install-btn'
    installBtn.innerHTML = '📱 Install App'
    installBtn.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      border: none;
      padding: 0.75rem 1rem;
      border-radius: 25px;
      cursor: pointer;
      font-weight: bold;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      transition: all 0.3s ease;
    `

    installBtn.addEventListener('mouseenter', () => {
      installBtn.style.transform = 'scale(1.05)'
    })

    installBtn.addEventListener('mouseleave', () => {
      installBtn.style.transform = 'scale(1)'
    })

    installBtn.addEventListener('click', async () => {
      if (!deferredPrompt) return

      // Show the install prompt
      deferredPrompt.prompt()

      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice

      if (outcome === 'accepted') {
        console.log('PWA: User accepted the install prompt')
      } else {
        console.log('PWA: User dismissed the install prompt')
      }

      // Clear the deferredPrompt
      deferredPrompt = null
      installBtn.remove()
    })

    document.body.appendChild(installBtn)

    // Auto hide after 30 seconds
    setTimeout(() => {
      if (installBtn.parentNode) {
        installBtn.remove()
      }
    }, 30000)
  }

  // Handle app installed
  window.addEventListener('appinstalled', () => {
    console.log('PWA: App was installed')
    
    // Remove install button if it exists
    const installBtn = document.querySelector('#pwa-install-btn')
    if (installBtn) {
      installBtn.remove()
    }

    // Track installation with Google Analytics
    if (typeof window.gtag === 'function') {
      window.gtag('event', 'pwa_installed', {
        event_category: 'pwa',
        event_label: 'app_installed'
      })
    }
  })

  // Performance monitoring
  const monitorPerformance = () => {
    if ('performance' in window && 'getEntriesByType' in performance) {
      // Monitor navigation timing
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0]
          if (navigation) {
            console.log('PWA: Navigation timing', {
              domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
              loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
              totalTime: navigation.loadEventEnd - navigation.fetchStart
            })

            // Track with Google Analytics
            if (typeof window.gtag === 'function') {
              window.gtag('event', 'page_load_time', {
                event_category: 'performance',
                value: Math.round(navigation.loadEventEnd - navigation.fetchStart),
                custom_parameter: 'navigation_timing'
              })
            }
          }
        }, 0)
      })
    }
  }

  // Cache management utilities
  window.clearAppCache = async () => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      return new Promise((resolve) => {
        const messageChannel = new MessageChannel()
        messageChannel.port1.onmessage = (event) => {
          resolve(event.data)
        }
        
        navigator.serviceWorker.controller.postMessage(
          { type: 'CLEAR_CACHE' },
          [messageChannel.port2]
        )
      })
    }
  }

  // Initialize PWA features
  const initPWA = async () => {
    // Register Service Worker
    await registerServiceWorker()
    
    // Start performance monitoring
    monitorPerformance()
    
    console.log('PWA: Initialized successfully')
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPWA)
  } else {
    initPWA()
  }
}
